"""
Notice management commands.

This module implements all notice/reminder-related CLI commands including add, list, update, delete.
"""

from typing import Optional, List
from datetime import datetime
import typer
from rich.table import Table
from rich.panel import Panel
from sqlalchemy.orm import Session

from todo.database import get_session
from todo.models.reminder import Reminder, RepeatMode
from todo.utils.display import print_success, print_error, print_info, console
from todo.utils.helpers import parse_notice_time, parse_repeat_mode, format_repeat_mode, format_date
from todo.services.scheduler import get_scheduler

# 创建提醒子命令组
app = typer.Typer(help="Notice/reminder management commands", no_args_is_help=True)


@app.command("add")
def add_notice(
    message: str = typer.Argument(..., help="Reminder message"),
    notice_time: str = typer.Argument(..., help="Notice time (YYYY-MM-DD HH:MM or relative like '30min', '2h', '1d')"),
    repeat: Optional[str] = typer.Option(None, "--repeat", "-r", help="Repeat mode (once/every_day/working_day/week/month/year)"),
    title: Optional[str] = typer.Option(None, "--title", "-t", help="Optional reminder title")
):
    """Add a new reminder notice."""
    db = get_session()
    try:
        # 解析提醒时间
        try:
            reminder_time = parse_notice_time(notice_time)
        except ValueError as e:
            print_error(f"Invalid notice time format: {e}")
            raise typer.Exit(1)
        
        # 解析重复模式
        repeat_mode = RepeatMode.ONCE
        if repeat:
            try:
                repeat_mode = parse_repeat_mode(repeat)
            except ValueError as e:
                print_error(f"Invalid repeat mode: {e}")
                raise typer.Exit(1)
        
        # 创建提醒
        reminder = Reminder(
            message=message,
            title=title or "提醒通知",
            notice_time=reminder_time,
            repeat_mode=repeat_mode
        )
        reminder.job_id = reminder.generate_job_id()
        
        db.add(reminder)
        db.commit()
        db.refresh(reminder)
        
        # 检查服务是否运行，如果运行则立即添加到调度器
        from todo.services.scheduler import is_service_running
        if is_service_running():
            scheduler = get_scheduler()
            if scheduler.add_reminder(reminder):
                print_success(f"Reminder created successfully with ID: {reminder.id}")
                print_info(f"Scheduled for: {reminder_time.strftime('%Y-%m-%d %H:%M')}")
                print_info(f"Repeat mode: {format_repeat_mode(repeat_mode)}")
                print_info("✅ Added to running scheduler")
            else:
                print_success(f"Reminder created successfully with ID: {reminder.id}")
                print_info(f"Scheduled for: {reminder_time.strftime('%Y-%m-%d %H:%M')}")
                print_info(f"Repeat mode: {format_repeat_mode(repeat_mode)}")
                print_info("⏳ Will be picked up by scheduler monitor")
        else:
            print_success(f"Reminder created successfully with ID: {reminder.id}")
            print_info(f"Scheduled for: {reminder_time.strftime('%Y-%m-%d %H:%M')}")
            print_info(f"Repeat mode: {format_repeat_mode(repeat_mode)}")
            print_info("⏳ Start service with 'todo service start' to activate reminders")
        
    except Exception as e:
        db.rollback()
        print_error(f"Failed to create reminder: {e}")
        raise typer.Exit(1)
    finally:
        db.close()


@app.command("list")
def list_notices(
    active_only: bool = typer.Option(True, "--active-only/--all", help="Show only active reminders"),
    limit: int = typer.Option(50, "--limit", "-l", help="Maximum number of reminders to show")
):
    """List all reminders."""
    db = get_session()
    try:
        query = db.query(Reminder)
        
        if active_only:
            query = query.filter(Reminder.is_active == True)
        
        query = query.order_by(Reminder.notice_time.asc()).limit(limit)
        reminders = query.all()
        
        if not reminders:
            print_info("No reminders found")
            return
        
        # 创建表格
        table = Table(title="Reminders")
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Title", style="magenta")
        table.add_column("Message", style="white")
        table.add_column("Notice Time", style="green")
        table.add_column("Repeat", style="yellow")
        table.add_column("Status", style="blue")
        table.add_column("Type", style="red")
        
        for reminder in reminders:
            status = "🟢 Active" if reminder.is_active else "🔴 Inactive"
            reminder_type = "📋 Task" if reminder.is_task_reminder else "🔔 Notice"
            
            table.add_row(
                str(reminder.id),
                reminder.title or "N/A",
                reminder.message[:50] + "..." if len(reminder.message) > 50 else reminder.message,
                format_date(reminder.notice_time),
                format_repeat_mode(reminder.repeat_mode),
                status,
                reminder_type
            )
        
        console.print(table)
        
    except Exception as e:
        print_error(f"Failed to list reminders: {e}")
        raise typer.Exit(1)
    finally:
        db.close()


@app.command("show")
def show_notice(notice_id: int):
    """Show detailed information about a specific reminder."""
    db = get_session()
    try:
        reminder = db.query(Reminder).filter(Reminder.id == notice_id).first()
        
        if not reminder:
            print_error(f"Reminder with ID {notice_id} not found")
            raise typer.Exit(1)
        
        # 创建详细信息面板
        info_text = f"""
[bold]ID:[/bold] {reminder.id}
[bold]Title:[/bold] {reminder.title or 'N/A'}
[bold]Message:[/bold] {reminder.message}
[bold]Notice Time:[/bold] {format_date(reminder.notice_time)}
[bold]Repeat Mode:[/bold] {format_repeat_mode(reminder.repeat_mode)}
[bold]Status:[/bold] {'🟢 Active' if reminder.is_active else '🔴 Inactive'}
[bold]Type:[/bold] {'📋 Task Reminder' if reminder.is_task_reminder else '🔔 Notice'}
[bold]Job ID:[/bold] {reminder.job_id or 'N/A'}
[bold]Created:[/bold] {format_date(reminder.created_at)}
[bold]Updated:[/bold] {format_date(reminder.updated_at)}
        """
        
        panel = Panel(info_text.strip(), title="Reminder Details", border_style="blue")
        console.print(panel)
        
    except Exception as e:
        print_error(f"Failed to show reminder: {e}")
        raise typer.Exit(1)
    finally:
        db.close()


@app.command("delete")
def delete_notice(notice_id: int):
    """Delete a reminder."""
    db = get_session()
    try:
        reminder = db.query(Reminder).filter(Reminder.id == notice_id).first()
        
        if not reminder:
            print_error(f"Reminder with ID {notice_id} not found")
            raise typer.Exit(1)
        
        # 从调度器中移除
        if reminder.job_id:
            scheduler = get_scheduler()
            scheduler.remove_reminder(reminder.job_id)
        
        # 从数据库中删除
        db.delete(reminder)
        db.commit()
        
        print_success(f"Reminder {notice_id} deleted successfully")
        
    except Exception as e:
        db.rollback()
        print_error(f"Failed to delete reminder: {e}")
        raise typer.Exit(1)
    finally:
        db.close()


@app.command("update")
def update_notice(
    notice_id: int,
    message: Optional[str] = typer.Option(None, "--message", "-m", help="New message"),
    notice_time: Optional[str] = typer.Option(None, "--time", "-t", help="New notice time"),
    repeat: Optional[str] = typer.Option(None, "--repeat", "-r", help="New repeat mode"),
    title: Optional[str] = typer.Option(None, "--title", help="New title"),
    active: Optional[bool] = typer.Option(None, "--active/--inactive", help="Set active status")
):
    """Update a reminder."""
    db = get_session()
    try:
        reminder = db.query(Reminder).filter(Reminder.id == notice_id).first()
        
        if not reminder:
            print_error(f"Reminder with ID {notice_id} not found")
            raise typer.Exit(1)
        
        # 更新字段
        updated = False
        
        if message is not None:
            reminder.message = message
            updated = True
        
        if title is not None:
            reminder.title = title
            updated = True
        
        if notice_time is not None:
            try:
                reminder.notice_time = parse_notice_time(notice_time)
                updated = True
            except ValueError as e:
                print_error(f"Invalid notice time format: {e}")
                raise typer.Exit(1)
        
        if repeat is not None:
            try:
                reminder.repeat_mode = parse_repeat_mode(repeat)
                updated = True
            except ValueError as e:
                print_error(f"Invalid repeat mode: {e}")
                raise typer.Exit(1)
        
        if active is not None:
            reminder.is_active = active
            updated = True
        
        if not updated:
            print_info("No changes specified")
            return
        
        # 更新数据库
        db.commit()
        
        # 重新调度
        scheduler = get_scheduler()
        if reminder.job_id:
            scheduler.remove_reminder(reminder.job_id)
        
        if reminder.is_active:
            reminder.job_id = reminder.generate_job_id()
            if scheduler.add_reminder(reminder):
                print_success(f"Reminder {notice_id} updated and rescheduled successfully")
            else:
                print_success(f"Reminder {notice_id} updated successfully")
                print_error("Failed to reschedule reminder")
        else:
            print_success(f"Reminder {notice_id} updated successfully")
        
    except Exception as e:
        db.rollback()
        print_error(f"Failed to update reminder: {e}")
        raise typer.Exit(1)
    finally:
        db.close()


@app.command("test")
def test_notice():
    """Test reminder popup immediately."""
    try:
        from todo.services.scheduler import show_reminder_popup
        print_info("Testing reminder popup...")
        show_reminder_popup("这是一个测试提醒", "测试提醒")
        print_success("Test reminder popup displayed")
    except Exception as e:
        print_error(f"Failed to show test reminder: {e}")
        raise typer.Exit(1)


@app.command("clear")
def clear_notices(
    inactive_only: bool = typer.Option(True, "--inactive-only/--all", help="Clear only inactive reminders"),
    confirm: bool = typer.Option(False, "--yes", "-y", help="Skip confirmation")
):
    """Clear reminders."""
    db = get_session()
    try:
        query = db.query(Reminder)

        if inactive_only:
            query = query.filter(Reminder.is_active == False)
            action = "inactive reminders"
        else:
            action = "all reminders"

        reminders = query.all()

        if not reminders:
            print_info(f"No {action} found")
            return

        if not confirm:
            response = typer.confirm(f"Are you sure you want to delete {len(reminders)} {action}?")
            if not response:
                print_info("Operation cancelled")
                return

        # 从调度器中移除
        scheduler = get_scheduler()
        for reminder in reminders:
            if reminder.job_id:
                scheduler.remove_reminder(reminder.job_id)

        # 从数据库中删除
        for reminder in reminders:
            db.delete(reminder)

        db.commit()
        print_success(f"Cleared {len(reminders)} {action}")

    except Exception as e:
        db.rollback()
        print_error(f"Failed to clear reminders: {e}")
        raise typer.Exit(1)
    finally:
        db.close()
