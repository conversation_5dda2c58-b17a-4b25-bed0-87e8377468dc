"""
Service management commands.

This module implements service management commands for the reminder scheduler.
"""

import os
import sys
import time
import signal
import psutil
import platform
import subprocess
from typing import Optional
from pathlib import Path
import typer
from rich.table import Table
from rich.panel import Panel

from todo.utils.display import print_success, print_error, print_info, console
from todo.services.scheduler import get_scheduler

# 创建服务子命令组
app = typer.Typer(help="Service management commands", no_args_is_help=True)

# 服务进程文件路径
def get_pid_file() -> Path:
    """获取PID文件路径"""
    app_dir = typer.get_app_dir("todo-cli")
    os.makedirs(app_dir, exist_ok=True)
    return Path(app_dir) / "scheduler.pid"


def get_log_file() -> Path:
    """获取日志文件路径"""
    app_dir = typer.get_app_dir("todo-cli")
    os.makedirs(app_dir, exist_ok=True)
    return Path(app_dir) / "scheduler.log"


def is_service_running() -> tuple[bool, Optional[int]]:
    """检查服务是否运行中"""
    pid_file = get_pid_file()
    
    if not pid_file.exists():
        return False, None
    
    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())
        
        # 检查进程是否存在
        if psutil.pid_exists(pid):
            proc = psutil.Process(pid)
            # 检查进程名称是否匹配
            if 'python' in proc.name().lower() and 'todo' in ' '.join(proc.cmdline()):
                return True, pid
        
        # PID文件存在但进程不存在，清理PID文件
        pid_file.unlink()
        return False, None
        
    except (ValueError, FileNotFoundError, psutil.NoSuchProcess):
        # PID文件损坏或进程不存在
        if pid_file.exists():
            pid_file.unlink()
        return False, None


def write_pid_file(pid: int):
    """写入PID文件"""
    pid_file = get_pid_file()
    with open(pid_file, 'w') as f:
        f.write(str(pid))


def remove_pid_file():
    """删除PID文件"""
    pid_file = get_pid_file()
    if pid_file.exists():
        pid_file.unlink()


def _start_daemon_unix():
    """Unix/Linux系统的守护进程启动"""
    # 创建子进程
    pid = os.fork()
    if pid > 0:
        # 父进程
        write_pid_file(pid)
        print_success(f"Service started successfully with PID: {pid}")
        print_info(f"Log file: {get_log_file()}")
        return

    # 子进程 - 成为守护进程
    os.setsid()
    os.chdir('/')
    os.umask(0)

    # 重定向标准输入输出
    log_file = get_log_file()
    with open(log_file, 'a') as f:
        os.dup2(f.fileno(), sys.stdout.fileno())
        os.dup2(f.fileno(), sys.stderr.fileno())

    # 启动调度器
    scheduler = get_scheduler()
    scheduler.start()

    # 保持运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        scheduler.stop()
        remove_pid_file()
        sys.exit(0)


def _start_daemon_windows():
    """Windows系统的后台进程启动"""
    # 获取当前Python可执行文件
    python_exe = sys.executable

    # daemon runner脚本路径（相对于项目根目录）
    daemon_runner_path = Path(__file__).parent.parent / "utils/daemon_runner.py"

    if not daemon_runner_path.exists():
        print_error(f"Daemon runner script not found: {daemon_runner_path}")
        print_info("Please ensure daemon_runner.py exists in the project root")
        raise typer.Exit(1)

    try:
        # 使用subprocess启动后台进程
        process = subprocess.Popen(
            [python_exe, str(daemon_runner_path)],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            stdin=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )

        # 等待一下确保进程启动
        time.sleep(2)

        if process.poll() is None:  # 进程仍在运行
            print_success(f"Service started successfully with PID: {process.pid}")
            print_info(f"Log file: {get_log_file()}")
        else:
            print_error("Failed to start service")

    except Exception as e:
        print_error(f"Failed to start daemon process: {e}")
        raise


@app.command("start")
def start_service(
    daemon: bool = typer.Option(True, "--daemon/--foreground", help="Run as daemon process"),
    force: bool = typer.Option(False, "--force", "-f", help="Force start even if already running")
):
    """Start the reminder service."""
    running, pid = is_service_running()

    if running and not force:
        print_info(f"Service is already running with PID: {pid}")
        return

    if running and force:
        print_info(f"Stopping existing service (PID: {pid})...")
        stop_service()
        time.sleep(2)

    try:
        if daemon:
            # 后台运行模式
            print_info("Starting reminder service in daemon mode...")

            # 根据操作系统选择不同的启动方式
            if os.name == 'nt':  # Windows
                _start_daemon_windows()
            else:  # Unix/Linux/macOS
                _start_daemon_unix()

        else:
            # 前台运行模式
            print_info("Starting reminder service in foreground mode...")
            print_info("Press Ctrl+C to stop the service")

            scheduler = get_scheduler()
            scheduler.start()

            print_success("Service started successfully")

            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print_info("\nStopping service...")
                scheduler.stop()
                print_success("Service stopped")

    except Exception as e:
        print_error(f"Failed to start service: {e}")
        raise typer.Exit(1)


@app.command("stop")
def stop_service():
    """Stop the reminder service."""
    running, pid = is_service_running()

    if not running:
        print_info("Service is not running")
        return

    try:
        print_info(f"Stopping service (PID: {pid})...")

        if os.name == 'nt':  # Windows
            # Windows使用taskkill命令
            try:
                subprocess.run(['taskkill', '/F', '/PID', str(pid)],
                             check=True, capture_output=True)
            except subprocess.CalledProcessError:
                # 如果taskkill失败，尝试使用psutil
                if psutil.pid_exists(pid):
                    proc = psutil.Process(pid)
                    proc.terminate()
                    proc.wait(timeout=10)
        else:
            # Unix/Linux/macOS使用信号
            os.kill(pid, signal.SIGTERM)

            # 等待进程结束
            for _ in range(10):  # 最多等待10秒
                if not psutil.pid_exists(pid):
                    break
                time.sleep(1)

            # 如果进程仍然存在，强制杀死
            if psutil.pid_exists(pid):
                print_info("Process not responding, force killing...")
                os.kill(pid, signal.SIGKILL)
                time.sleep(1)

        remove_pid_file()
        print_success("Service stopped successfully")

    except ProcessLookupError:
        # 进程已经不存在
        remove_pid_file()
        print_success("Service stopped")
    except Exception as e:
        print_error(f"Failed to stop service: {e}")
        raise typer.Exit(1)


@app.command("restart")
def restart_service():
    """Restart the reminder service."""
    print_info("Restarting service...")
    stop_service()
    time.sleep(2)
    start_service()


@app.command("status")
def service_status():
    """Check service status."""
    running, pid = is_service_running()
    
    if running:
        try:
            proc = psutil.Process(pid)
            
            # 获取进程信息
            create_time = proc.create_time()
            memory_info = proc.memory_info()
            cpu_percent = proc.cpu_percent()
            
            # 获取调度器状态（通过检查服务进程状态）
            try:
                from todo.services.scheduler import is_service_running as check_service_running
                service_running = check_service_running()
                scheduler_status = "🟢 Running" if service_running else "🔴 Stopped"

                # 如果服务运行中，尝试获取作业数量
                if service_running:
                    try:
                        # 通过数据库查询活跃的提醒数量
                        from todo.database import get_session
                        from todo.models.reminder import Reminder
                        db = get_session()
                        job_count = db.query(Reminder).filter(Reminder.is_active == True).count()
                        db.close()
                    except:
                        job_count = "N/A"
                else:
                    job_count = 0
            except:
                job_count = "N/A"
                scheduler_status = "❓ Unknown"
            
            # 创建状态表格
            table = Table(title="Service Status")
            table.add_column("Property", style="cyan")
            table.add_column("Value", style="white")
            
            table.add_row("Status", "🟢 Running")
            table.add_row("PID", str(pid))
            table.add_row("Started", time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(create_time)))
            table.add_row("Memory Usage", f"{memory_info.rss / 1024 / 1024:.1f} MB")
            table.add_row("CPU Usage", f"{cpu_percent:.1f}%")
            table.add_row("Scheduler", scheduler_status)
            table.add_row("Active Jobs", str(job_count))
            table.add_row("Log File", str(get_log_file()))
            
            console.print(table)
            
        except psutil.NoSuchProcess:
            print_error("Service process not found")
            remove_pid_file()
        except Exception as e:
            print_error(f"Failed to get service status: {e}")
    else:
        print_info("🔴 Service is not running")


@app.command("logs")
def show_logs(
    lines: int = typer.Option(50, "--lines", "-n", help="Number of lines to show"),
    follow: bool = typer.Option(False, "--follow", "-f", help="Follow log output")
):
    """Show service logs."""
    log_file = get_log_file()
    
    if not log_file.exists():
        print_info("No log file found")
        return
    
    try:
        if follow:
            print_info(f"Following log file: {log_file}")
            print_info("Press Ctrl+C to stop")
            
            # 简单的tail -f实现
            with open(log_file, 'r') as f:
                # 移动到文件末尾
                f.seek(0, 2)
                
                try:
                    while True:
                        line = f.readline()
                        if line:
                            print(line.rstrip())
                        else:
                            time.sleep(0.1)
                except KeyboardInterrupt:
                    print_info("\nStopped following logs")
        else:
            # 显示最后N行
            with open(log_file, 'r') as f:
                lines_list = f.readlines()
                
            if lines_list:
                start_index = max(0, len(lines_list) - lines)
                for line in lines_list[start_index:]:
                    print(line.rstrip())
            else:
                print_info("Log file is empty")
                
    except Exception as e:
        print_error(f"Failed to read log file: {e}")
        raise typer.Exit(1)


@app.command("jobs")
def list_jobs():
    """List active scheduler jobs."""
    try:
        from todo.services.scheduler import is_service_running as check_service_running

        if not check_service_running():
            print_info("Scheduler service is not running")
            return

        # 通过数据库查询活跃的提醒
        from todo.database import get_session
        from todo.models.reminder import Reminder

        db = get_session()
        try:
            reminders = db.query(Reminder).filter(Reminder.is_active == True).all()

            if not reminders:
                print_info("No active reminders found")
                return

            # 创建作业表格
            table = Table(title="Active Reminders")
            table.add_column("ID", style="cyan")
            table.add_column("Title", style="magenta")
            table.add_column("Message", style="white")
            table.add_column("Next Run", style="green")
            table.add_column("Repeat", style="yellow")
            table.add_column("Type", style="blue")

            for reminder in reminders:
                reminder_type = "📋 Task" if reminder.is_task_reminder else "🔔 Notice"

                table.add_row(
                    str(reminder.id),
                    reminder.title or 'N/A',
                    reminder.message[:30] + "..." if len(reminder.message) > 30 else reminder.message,
                    reminder.notice_time.strftime('%Y-%m-%d %H:%M'),
                    reminder.repeat_mode.value,
                    reminder_type
                )

            console.print(table)
            print_info(f"Total active reminders: {len(reminders)}")

        finally:
            db.close()

    except Exception as e:
        print_error(f"Failed to list jobs: {e}")
        raise typer.Exit(1)
