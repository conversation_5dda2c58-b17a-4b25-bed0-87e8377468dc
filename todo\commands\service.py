"""
Service management commands.

This module implements service management commands for the reminder scheduler.
"""

import os
import sys
import time
import signal
import psutil
from typing import Optional
from pathlib import Path
import typer
from rich.table import Table
from rich.panel import Panel

from todo.utils.display import print_success, print_error, print_info, console
from todo.services.scheduler import get_scheduler

# 创建服务子命令组
app = typer.Typer(help="Service management commands", no_args_is_help=True)

# 服务进程文件路径
def get_pid_file() -> Path:
    """获取PID文件路径"""
    app_dir = typer.get_app_dir("todo-cli")
    os.makedirs(app_dir, exist_ok=True)
    return Path(app_dir) / "scheduler.pid"


def get_log_file() -> Path:
    """获取日志文件路径"""
    app_dir = typer.get_app_dir("todo-cli")
    os.makedirs(app_dir, exist_ok=True)
    return Path(app_dir) / "scheduler.log"


def is_service_running() -> tuple[bool, Optional[int]]:
    """检查服务是否运行中"""
    pid_file = get_pid_file()
    
    if not pid_file.exists():
        return False, None
    
    try:
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())
        
        # 检查进程是否存在
        if psutil.pid_exists(pid):
            proc = psutil.Process(pid)
            # 检查进程名称是否匹配
            if 'python' in proc.name().lower() and 'todo' in ' '.join(proc.cmdline()):
                return True, pid
        
        # PID文件存在但进程不存在，清理PID文件
        pid_file.unlink()
        return False, None
        
    except (ValueError, FileNotFoundError, psutil.NoSuchProcess):
        # PID文件损坏或进程不存在
        if pid_file.exists():
            pid_file.unlink()
        return False, None


def write_pid_file(pid: int):
    """写入PID文件"""
    pid_file = get_pid_file()
    with open(pid_file, 'w') as f:
        f.write(str(pid))


def remove_pid_file():
    """删除PID文件"""
    pid_file = get_pid_file()
    if pid_file.exists():
        pid_file.unlink()


@app.command("start")
def start_service(
    daemon: bool = typer.Option(True, "--daemon/--foreground", help="Run as daemon process"),
    force: bool = typer.Option(False, "--force", "-f", help="Force start even if already running")
):
    """Start the reminder service."""
    running, pid = is_service_running()
    
    if running and not force:
        print_info(f"Service is already running with PID: {pid}")
        return
    
    if running and force:
        print_info(f"Stopping existing service (PID: {pid})...")
        stop_service()
        time.sleep(2)
    
    try:
        if daemon:
            # 后台运行模式
            print_info("Starting reminder service in daemon mode...")
            
            # 创建子进程
            pid = os.fork()
            if pid > 0:
                # 父进程
                write_pid_file(pid)
                print_success(f"Service started successfully with PID: {pid}")
                print_info(f"Log file: {get_log_file()}")
                return
            
            # 子进程 - 成为守护进程
            os.setsid()
            os.chdir('/')
            os.umask(0)
            
            # 重定向标准输入输出
            log_file = get_log_file()
            with open(log_file, 'a') as f:
                os.dup2(f.fileno(), sys.stdout.fileno())
                os.dup2(f.fileno(), sys.stderr.fileno())
            
            # 启动调度器
            scheduler = get_scheduler()
            scheduler.start()
            
            # 保持运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                scheduler.stop()
                remove_pid_file()
                sys.exit(0)
        
        else:
            # 前台运行模式
            print_info("Starting reminder service in foreground mode...")
            print_info("Press Ctrl+C to stop the service")
            
            scheduler = get_scheduler()
            scheduler.start()
            
            print_success("Service started successfully")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print_info("\nStopping service...")
                scheduler.stop()
                print_success("Service stopped")
    
    except Exception as e:
        print_error(f"Failed to start service: {e}")
        raise typer.Exit(1)


@app.command("stop")
def stop_service():
    """Stop the reminder service."""
    running, pid = is_service_running()
    
    if not running:
        print_info("Service is not running")
        return
    
    try:
        print_info(f"Stopping service (PID: {pid})...")
        
        # 发送TERM信号
        os.kill(pid, signal.SIGTERM)
        
        # 等待进程结束
        for _ in range(10):  # 最多等待10秒
            if not psutil.pid_exists(pid):
                break
            time.sleep(1)
        
        # 如果进程仍然存在，强制杀死
        if psutil.pid_exists(pid):
            print_info("Process not responding, force killing...")
            os.kill(pid, signal.SIGKILL)
            time.sleep(1)
        
        remove_pid_file()
        print_success("Service stopped successfully")
        
    except ProcessLookupError:
        # 进程已经不存在
        remove_pid_file()
        print_success("Service stopped")
    except Exception as e:
        print_error(f"Failed to stop service: {e}")
        raise typer.Exit(1)


@app.command("restart")
def restart_service():
    """Restart the reminder service."""
    print_info("Restarting service...")
    stop_service()
    time.sleep(2)
    start_service()


@app.command("status")
def service_status():
    """Check service status."""
    running, pid = is_service_running()
    
    if running:
        try:
            proc = psutil.Process(pid)
            
            # 获取进程信息
            create_time = proc.create_time()
            memory_info = proc.memory_info()
            cpu_percent = proc.cpu_percent()
            
            # 获取调度器状态
            try:
                scheduler = get_scheduler()
                job_count = scheduler.get_job_count() if scheduler.is_running() else 0
                scheduler_status = "🟢 Running" if scheduler.is_running() else "🔴 Stopped"
            except:
                job_count = "N/A"
                scheduler_status = "❓ Unknown"
            
            # 创建状态表格
            table = Table(title="Service Status")
            table.add_column("Property", style="cyan")
            table.add_column("Value", style="white")
            
            table.add_row("Status", "🟢 Running")
            table.add_row("PID", str(pid))
            table.add_row("Started", time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(create_time)))
            table.add_row("Memory Usage", f"{memory_info.rss / 1024 / 1024:.1f} MB")
            table.add_row("CPU Usage", f"{cpu_percent:.1f}%")
            table.add_row("Scheduler", scheduler_status)
            table.add_row("Active Jobs", str(job_count))
            table.add_row("Log File", str(get_log_file()))
            
            console.print(table)
            
        except psutil.NoSuchProcess:
            print_error("Service process not found")
            remove_pid_file()
        except Exception as e:
            print_error(f"Failed to get service status: {e}")
    else:
        print_info("🔴 Service is not running")


@app.command("logs")
def show_logs(
    lines: int = typer.Option(50, "--lines", "-n", help="Number of lines to show"),
    follow: bool = typer.Option(False, "--follow", "-f", help="Follow log output")
):
    """Show service logs."""
    log_file = get_log_file()
    
    if not log_file.exists():
        print_info("No log file found")
        return
    
    try:
        if follow:
            print_info(f"Following log file: {log_file}")
            print_info("Press Ctrl+C to stop")
            
            # 简单的tail -f实现
            with open(log_file, 'r') as f:
                # 移动到文件末尾
                f.seek(0, 2)
                
                try:
                    while True:
                        line = f.readline()
                        if line:
                            print(line.rstrip())
                        else:
                            time.sleep(0.1)
                except KeyboardInterrupt:
                    print_info("\nStopped following logs")
        else:
            # 显示最后N行
            with open(log_file, 'r') as f:
                lines_list = f.readlines()
                
            if lines_list:
                start_index = max(0, len(lines_list) - lines)
                for line in lines_list[start_index:]:
                    print(line.rstrip())
            else:
                print_info("Log file is empty")
                
    except Exception as e:
        print_error(f"Failed to read log file: {e}")
        raise typer.Exit(1)


@app.command("jobs")
def list_jobs():
    """List active scheduler jobs."""
    try:
        scheduler = get_scheduler()
        
        if not scheduler.is_running():
            print_info("Scheduler is not running")
            return
        
        jobs = scheduler.list_jobs()
        
        if not jobs:
            print_info("No active jobs found")
            return
        
        # 创建作业表格
        table = Table(title="Active Scheduler Jobs")
        table.add_column("Job ID", style="cyan")
        table.add_column("Name", style="magenta")
        table.add_column("Next Run", style="green")
        table.add_column("Trigger", style="yellow")
        
        for job in jobs:
            table.add_row(
                job['id'],
                job['name'] or 'N/A',
                str(job['next_run_time']) if job['next_run_time'] else 'N/A',
                job['trigger']
            )
        
        console.print(table)
        
    except Exception as e:
        print_error(f"Failed to list jobs: {e}")
        raise typer.Exit(1)
