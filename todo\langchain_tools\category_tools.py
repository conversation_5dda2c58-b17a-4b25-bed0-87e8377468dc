"""
分类管理相关的Langchain工具
"""

from typing import Optional, Dict, Any
from langchain_core.tools import tool

from todo.database import get_session
from todo.models.task import Task
from todo.models.category import Category

@tool
def add_category(
    name: str,
    description: Optional[str] = None,
    color: Optional[str] = None
) -> Dict[str, Any]:
    """创建新分类

    Args:
        name: 分类名称
        description: 分类描述（可选）
        color: 分类颜色，十六进制格式如 #FF5733（可选）

    Returns:
        包含分类信息的字典
    """
    db = get_session()
    try:
        # 检查分类是否已存在
        existing = db.query(Category).filter(Category.name == name).first()
        if existing:
            return {"error": f"Category '{name}' already exists"}

        # 验证颜色格式
        if color and not color.startswith('#'):
            color = f"#{color}"

        if color and len(color) != 7:
            return {"error": "Color must be a valid hex code (e.g., #FF5733)"}

        # 创建分类
        category = Category(
            name=name,
            description=description,
            color=color
        )

        db.add(category)
        db.commit()
        db.refresh(category)

        return {
            "success": True,
            "category_id": category.id,
            "name": category.name,
            "description": category.description,
            "color": category.color,
            "created_at": category.created_at.isoformat() if category.created_at else None
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to create category: {str(e)}"}
    finally:
        db.close()


@tool
def list_categories() -> Dict[str, Any]:
    """列出所有分类

    Returns:
        包含分类列表的字典
    """
    db = get_session()
    try:
        categories = db.query(Category).order_by(Category.name).all()

        category_list = []
        for category in categories:
            # 计算该分类下的任务数量
            task_count = db.query(Task).filter(Task.category_id == category.id).count()

            category_list.append({
                "id": category.id,
                "name": category.name,
                "description": category.description,
                "color": category.color,
                "task_count": task_count,
                "created_at": category.created_at.isoformat() if category.created_at else None
            })

        return {
            "success": True,
            "categories": category_list,
            "total_count": len(category_list)
        }

    except Exception as e:
        return {"error": f"Failed to list categories: {str(e)}"}
    finally:
        db.close()


@tool
def update_category(
    name: str,
    new_name: Optional[str] = None,
    description: Optional[str] = None,
    color: Optional[str] = None
) -> Dict[str, Any]:
    """更新分类

    Args:
        name: 要更新的分类名称
        new_name: 新的分类名称
        description: 新的描述
        color: 新的颜色（十六进制格式）

    Returns:
        包含更新结果的字典
    """
    db = get_session()
    try:
        category = db.query(Category).filter(Category.name == name).first()
        if not category:
            return {"error": f"Category '{name}' not found"}

        updated = False

        if new_name:
            # 检查新名称是否已存在
            existing = db.query(Category).filter(Category.name == new_name).first()
            if existing and existing.id != category.id:
                return {"error": f"Category '{new_name}' already exists"}
            category.name = new_name
            updated = True

        if description is not None:
            category.description = description
            updated = True

        if color:
            if not color.startswith('#'):
                color = f"#{color}"
            if len(color) != 7:
                return {"error": "Color must be a valid hex code (e.g., #FF5733)"}
            category.color = color
            updated = True

        if not updated:
            return {"error": "No changes specified"}

        db.commit()

        return {
            "success": True,
            "message": f"Category updated successfully",
            "category": {
                "id": category.id,
                "name": category.name,
                "description": category.description,
                "color": category.color,
                "updated_at": category.updated_at.isoformat() if category.updated_at else None
            }
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to update category: {str(e)}"}
    finally:
        db.close()


@tool
def delete_category(name: str) -> Dict[str, Any]:
    """删除分类

    Args:
        name: 分类名称

    Returns:
        包含删除结果的字典
    """
    db = get_session()
    try:
        category = db.query(Category).filter(Category.name == name).first()
        if not category:
            return {"error": f"Category '{name}' not found"}

        # 检查是否有任务使用该分类
        task_count = db.query(Task).filter(Task.category_id == category.id).count()
        if task_count > 0:
            return {"error": f"Cannot delete category '{name}' because it has {task_count} associated tasks"}

        db.delete(category)
        db.commit()

        return {
            "success": True,
            "message": f"Category '{name}' deleted successfully"
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to delete category: {str(e)}"}
    finally:
        db.close()

CATEGORY_TOOLS = [
    add_category,
    list_categories,
    update_category,
    delete_category,
]
