"""
Helper functions for formatting and utility operations.
"""

import re
from datetime import datetime, timedelta
from typing import Optional
from todo.models.task import TaskStatus, TaskPriority
from todo.models.reminder import RepeatMode


def format_date(date: Optional[datetime]) -> str:
    """格式化日期显示"""
    if not date:
        return "N/A"
    return date.strftime("%Y-%m-%d %H:%M")


def format_priority(priority: TaskPriority) -> str:
    """格式化优先级显示"""
    priority_map = {
        TaskPriority.LOW: "🟢 Low",
        TaskPriority.MEDIUM: "🟡 Medium", 
        TaskPriority.HIGH: "🟠 High",
        TaskPriority.URGENT: "🔴 Urgent"
    }
    return priority_map.get(priority, str(priority.value))


def format_status(status: TaskStatus) -> str:
    """格式化状态显示"""
    status_map = {
        TaskStatus.PENDING: "⏳ Pending",
        TaskStatus.IN_PROGRESS: "🔄 In Progress",
        TaskStatus.COMPLETED: "✅ Completed",
        TaskStatus.CANCELLED: "❌ Cancelled"
    }
    return status_map.get(status, str(status.value))


def parse_priority(priority_str: str) -> TaskPriority:
    """解析优先级字符串"""
    priority_map = {
        "low": TaskPriority.LOW,
        "medium": TaskPriority.MEDIUM,
        "high": TaskPriority.HIGH,
        "urgent": TaskPriority.URGENT,
        "l": TaskPriority.LOW,
        "m": TaskPriority.MEDIUM,
        "h": TaskPriority.HIGH,
        "u": TaskPriority.URGENT
    }
    return priority_map.get(priority_str.lower(), TaskPriority.MEDIUM)


def parse_status(status_str: str) -> TaskStatus:
    """解析状态字符串"""
    status_map = {
        "pending": TaskStatus.PENDING,
        "in_progress": TaskStatus.IN_PROGRESS,
        "completed": TaskStatus.COMPLETED,
        "cancelled": TaskStatus.CANCELLED,
        "p": TaskStatus.PENDING,
        "i": TaskStatus.IN_PROGRESS,
        "c": TaskStatus.COMPLETED,
        "x": TaskStatus.CANCELLED
    }
    return status_map.get(status_str.lower(), TaskStatus.PENDING)


def parse_date(date_str: str) -> Optional[datetime]:
    """解析日期字符串"""
    if not date_str:
        return None
    
    formats = [
        "%Y-%m-%d",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d %H:%M:%S",
        "%m-%d",
        "%m/%d",
        "%Y/%m/%d"
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    raise ValueError(f"Invalid date format: {date_str}")


def truncate_text(text: str, max_length: int = 50) -> str:
    """截断文本并添加省略号"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def parse_relative_time(time_str: str) -> datetime:
    """解析相对时间格式，如 '30min', '2h', '1d'"""
    pattern = r'^(\d+)(min|m|h|d)$'
    match = re.match(pattern, time_str.lower())

    if not match:
        raise ValueError(f"Invalid relative time format: {time_str}")

    amount = int(match.group(1))
    unit = match.group(2)

    now = datetime.now()

    if unit in ['min', 'm']:
        return now + timedelta(minutes=amount)
    elif unit == 'h':
        return now + timedelta(hours=amount)
    elif unit == 'd':
        return now + timedelta(days=amount)
    else:
        raise ValueError(f"Unsupported time unit: {unit}")


def parse_absolute_time(time_str: str) -> datetime:
    """解析绝对时间格式，如 '2024-01-15 14:30' 或 '14:30'"""
    formats = [
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d %H:%M:%S",
        "%H:%M",
        "%m-%d %H:%M",
        "%Y/%m/%d %H:%M"
    ]

    for fmt in formats:
        try:
            parsed_time = datetime.strptime(time_str, fmt)

            # 如果只有时间没有日期，使用今天的日期
            if fmt == "%H:%M":
                now = datetime.now()
                parsed_time = parsed_time.replace(
                    year=now.year,
                    month=now.month,
                    day=now.day
                )
                # 如果时间已经过了，设置为明天
                if parsed_time <= now:
                    parsed_time += timedelta(days=1)

            # 如果只有月日没有年份，使用今年
            elif fmt == "%m-%d %H:%M":
                now = datetime.now()
                parsed_time = parsed_time.replace(year=now.year)
                # 如果日期已经过了，设置为明年
                if parsed_time <= now:
                    parsed_time = parsed_time.replace(year=now.year + 1)

            return parsed_time

        except ValueError:
            continue

    raise ValueError(f"Invalid time format: {time_str}")


def parse_notice_time(time_str: str) -> datetime:
    """解析提醒时间，支持绝对和相对时间格式"""
    if not time_str:
        raise ValueError("Time string cannot be empty")

    # 检查是否为相对时间格式
    if re.match(r'^\d+[mhd]', time_str.lower()):
        return parse_relative_time(time_str)
    else:
        return parse_absolute_time(time_str)


def parse_repeat_mode(repeat_str: str) -> RepeatMode:
    """解析重复模式字符串"""
    repeat_map = {
        "once": RepeatMode.ONCE,
        "every_day": RepeatMode.EVERY_DAY,
        "daily": RepeatMode.EVERY_DAY,
        "working_day": RepeatMode.WORKING_DAY,
        "workday": RepeatMode.WORKING_DAY,
        "week": RepeatMode.WEEK,
        "weekly": RepeatMode.WEEK,
        "month": RepeatMode.MONTH,
        "monthly": RepeatMode.MONTH,
        "year": RepeatMode.YEAR,
        "yearly": RepeatMode.YEAR,
        "annual": RepeatMode.YEAR
    }
    return repeat_map.get(repeat_str.lower(), RepeatMode.ONCE)


def format_repeat_mode(repeat_mode: RepeatMode) -> str:
    """格式化重复模式显示"""
    repeat_map = {
        RepeatMode.ONCE: "一次性",
        RepeatMode.EVERY_DAY: "每天",
        RepeatMode.WORKING_DAY: "工作日",
        RepeatMode.WEEK: "每周",
        RepeatMode.MONTH: "每月",
        RepeatMode.YEAR: "每年"
    }
    return repeat_map.get(repeat_mode, str(repeat_mode.value))
