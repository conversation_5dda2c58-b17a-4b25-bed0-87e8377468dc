#!/usr/bin/env python3
"""
测试完整的提醒流程
"""

import sys
import time
import subprocess
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=project_root)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def test_service_management():
    """测试服务管理"""
    print("🧪 测试服务管理...")
    
    # 停止现有服务
    print("停止现有服务...")
    success, stdout, stderr = run_command(" todo service stop")
    print(f"Stop result: {success}")
    
    # 启动服务
    print("启动服务...")
    success, stdout, stderr = run_command(" todo service start")
    print(f"Start result: {success}")
    if not success:
        print(f"Error: {stderr}")
        return False
    
    # 等待服务启动
    time.sleep(3)
    
    # 检查状态
    print("检查服务状态...")
    success, stdout, stderr = run_command(" todo service status")
    print(f"Status result: {success}")
    print(f"Status output: {stdout}")
    
    return success

def test_notice_creation():
    """测试创建提醒"""
    print("🧪 测试创建提醒...")
    
    # 创建1分钟后的提醒
    notice_time = (datetime.now() + timedelta(minutes=1)).strftime("%H:%M")
    cmd = f' todo notice add "测试提醒1分钟后" "{notice_time}"'
    
    print(f"执行命令: {cmd}")
    success, stdout, stderr = run_command(cmd)
    print(f"Create result: {success}")
    print(f"Output: {stdout}")
    if stderr:
        print(f"Error: {stderr}")
    
    return success

def test_notice_list():
    """测试查看提醒列表"""
    print("🧪 测试查看提醒列表...")
    
    success, stdout, stderr = run_command(" todo notice list")
    print(f"List result: {success}")
    print(f"Output: {stdout}")
    if stderr:
        print(f"Error: {stderr}")
    
    return success

def test_service_jobs():
    """测试查看服务作业"""
    print("🧪 测试查看服务作业...")
    
    success, stdout, stderr = run_command(" todo service jobs")
    print(f"Jobs result: {success}")
    print(f"Output: {stdout}")
    if stderr:
        print(f"Error: {stderr}")
    
    return success

def monitor_logs():
    """监控日志文件"""
    print("🧪 监控日志文件...")
    
    log_file = project_root / "scheduler_daemon.log"
    if log_file.exists():
        print("最新日志内容:")
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # 显示最后10行
            for line in lines[-10:]:
                print(f"  {line.strip()}")
    else:
        print("日志文件不存在")

def main():
    """主函数"""
    print("🔍 开始完整流程测试...\n")
    
    tests = [
        ("服务管理", test_service_management),
        ("创建提醒", test_notice_creation),
        ("查看提醒列表", test_notice_list),
        ("查看服务作业", test_service_jobs),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {name}")
        print('='*50)
        
        result = test_func()
        results.append((name, result))
        
        if not result:
            print(f"❌ {name} 失败，停止后续测试")
            break
        
        print(f"✅ {name} 成功")
        time.sleep(2)
    
    print(f"\n{'='*50}")
    print("日志监控")
    print('='*50)
    monitor_logs()
    
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print('='*50)
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    print(f"\n🎯 下一步:")
    print("1. 如果所有测试通过，等待1分钟看提醒是否弹出")
    print("2. 检查scheduler_daemon.log文件查看详细日志")
    print("3. 使用 ' todo service logs' 查看服务日志")

if __name__ == "__main__":
    main()
