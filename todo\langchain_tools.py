"""
Langchain 工具方法模块

将 Todo CLI 的功能封装为 Langchain 工具，支持 AI 集成调用。
包含任务、分类、标签、提醒的完整 CRUD 操作。

此文件现在作为兼容性入口，实际工具实现已模块化到 langchain_tools/ 目录下。
"""

# 导入模块化的工具
from todo.langchain_tools import (
    ALL_TOOLS,
    COMMON_TOOLS,
    TASK_TOOLS,
    REMINDER_TOOLS,
    CATEGORY_TOOLS,
    TAG_TOOLS,
    COMMON_TOOLS
)

# 为了向后兼容，重新导出所有工具
# 实际工具实现已移动到 langchain_tools/ 模块中

__all__ = [
    "ALL_TOOLS",
    "COMMON_TOOLS",
    "TASK_TOOLS",
    "REMINDER_TOOLS",
    "CATEGORY_TOOLS",
    "TAG_TOOLS",
]