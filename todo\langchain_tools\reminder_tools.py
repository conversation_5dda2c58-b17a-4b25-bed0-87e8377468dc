"""
提醒管理相关的Langchain工具
"""

from typing import Optional, Dict, Any
from datetime import datetime
from langchain_core.tools import tool

from todo.database import get_session
from todo.models.reminder import Reminder, RepeatMode
from todo.utils.helpers import parse_notice_time, parse_repeat_mode
from todo.services.scheduler import get_scheduler


@tool
def add_reminder(
    message: str,
    notice_time: str,
    repeat_mode: str = "once",
    title: Optional[str] = None
) -> Dict[str, Any]:
    """创建独立提醒

    Args:
        message: 提醒消息
        notice_time: 提醒时间，支持绝对时间(YYYY-MM-DD HH:MM)或相对时间(30min, 2h, 1d)
        repeat_mode: 重复模式 (once/every_day/working_day/week/month/year)，默认 once
        title: 提醒标题（可选）

    Returns:
        包含提醒信息的字典
    """
    db = get_session()
    try:
        # 解析提醒时间
        try:
            reminder_time = parse_notice_time(notice_time)
        except ValueError as e:
            return {"error": f"Invalid notice time format: {e}"}

        # 解析重复模式
        try:
            repeat_enum = parse_repeat_mode(repeat_mode)
        except ValueError as e:
            return {"error": f"Invalid repeat mode: {e}"}

        # 创建提醒
        reminder = Reminder(
            message=message,
            title=title or "提醒通知",
            notice_time=reminder_time,
            repeat_mode=repeat_enum
        )
        reminder.job_id = reminder.generate_job_id()

        db.add(reminder)
        db.commit()
        db.refresh(reminder)

        # 添加到调度器
        scheduler = get_scheduler()
        if scheduler.add_reminder(reminder):
            return {
                "success": True,
                "reminder_id": reminder.id,
                "message": reminder.message,
                "title": reminder.title,
                "notice_time": reminder.notice_time.isoformat(),
                "repeat_mode": reminder.repeat_mode.value,
                "status": "Reminder scheduled successfully"
            }
        else:
            return {
                "success": True,
                "reminder_id": reminder.id,
                "message": reminder.message,
                "notice_time": reminder.notice_time.isoformat(),
                "repeat_mode": reminder.repeat_mode.value,
                "status": "Reminder created but failed to schedule"
            }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to create reminder: {str(e)}"}
    finally:
        db.close()


@tool
def list_reminders(
    active_only: bool = True,
    limit: int = 50
) -> Dict[str, Any]:
    """列出提醒

    Args:
        active_only: 仅显示活跃提醒，默认 True
        limit: 最大返回数量，默认 50

    Returns:
        包含提醒列表的字典
    """
    db = get_session()
    try:
        query = db.query(Reminder)

        if active_only:
            query = query.filter(Reminder.is_active == True)

        query = query.order_by(Reminder.notice_time.asc()).limit(limit)
        reminders = query.all()

        reminder_list = []
        for reminder in reminders:
            reminder_list.append({
                "id": reminder.id,
                "title": reminder.title,
                "message": reminder.message,
                "notice_time": reminder.notice_time.isoformat(),
                "repeat_mode": reminder.repeat_mode.value,
                "is_active": reminder.is_active,
                "is_task_reminder": reminder.is_task_reminder,
                "task_id": reminder.task_id,
                "created_at": reminder.created_at.isoformat() if reminder.created_at else None
            })

        return {
            "success": True,
            "reminders": reminder_list,
            "total_count": len(reminder_list)
        }

    except Exception as e:
        return {"error": f"Failed to list reminders: {str(e)}"}
    finally:
        db.close()


@tool
def update_reminder(
    reminder_id: int,
    message: Optional[str] = None,
    notice_time: Optional[str] = None,
    repeat_mode: Optional[str] = None,
    title: Optional[str] = None,
    is_active: Optional[bool] = None
) -> Dict[str, Any]:
    """更新提醒

    Args:
        reminder_id: 提醒 ID
        message: 新消息
        notice_time: 新提醒时间
        repeat_mode: 新重复模式
        title: 新标题
        is_active: 是否激活

    Returns:
        包含更新结果的字典
    """
    db = get_session()
    try:
        reminder = db.query(Reminder).filter(Reminder.id == reminder_id).first()
        if not reminder:
            return {"error": f"Reminder with ID {reminder_id} not found"}

        updated = False

        if message is not None:
            reminder.message = message
            updated = True

        if title is not None:
            reminder.title = title
            updated = True

        if notice_time is not None:
            try:
                reminder.notice_time = parse_notice_time(notice_time)
                updated = True
            except ValueError as e:
                return {"error": f"Invalid notice time format: {e}"}

        if repeat_mode is not None:
            try:
                reminder.repeat_mode = parse_repeat_mode(repeat_mode)
                updated = True
            except ValueError as e:
                return {"error": f"Invalid repeat mode: {e}"}

        if is_active is not None:
            reminder.is_active = is_active
            updated = True

        if not updated:
            return {"error": "No changes specified"}

        db.commit()

        # 重新调度
        scheduler = get_scheduler()
        if reminder.job_id:
            scheduler.remove_reminder(reminder.job_id)

        if reminder.is_active:
            reminder.job_id = reminder.generate_job_id()
            scheduler.add_reminder(reminder)

        return {
            "success": True,
            "message": f"Reminder {reminder_id} updated successfully",
            "reminder": {
                "id": reminder.id,
                "title": reminder.title,
                "message": reminder.message,
                "notice_time": reminder.notice_time.isoformat(),
                "repeat_mode": reminder.repeat_mode.value,
                "is_active": reminder.is_active
            }
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to update reminder: {str(e)}"}
    finally:
        db.close()


@tool
def delete_reminder(reminder_id: int) -> Dict[str, Any]:
    """删除提醒

    Args:
        reminder_id: 提醒 ID

    Returns:
        包含删除结果的字典
    """
    db = get_session()
    try:
        reminder = db.query(Reminder).filter(Reminder.id == reminder_id).first()
        if not reminder:
            return {"error": f"Reminder with ID {reminder_id} not found"}

        # 从调度器中移除
        if reminder.job_id:
            scheduler = get_scheduler()
            scheduler.remove_reminder(reminder.job_id)

        reminder_message = reminder.message
        db.delete(reminder)
        db.commit()

        return {
            "success": True,
            "message": f"Reminder '{reminder_message}' (ID: {reminder_id}) deleted successfully"
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to delete reminder: {str(e)}"}
    finally:
        db.close()


# 导出提醒相关工具
REMINDER_TOOLS = [
    add_reminder,
    list_reminders,
    update_reminder,
    delete_reminder,
]
