#!/usr/bin/env python3
"""
调试提醒功能的脚本
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_popup():
    """测试弹窗功能"""
    print("🧪 测试弹窗功能...")
    try:
        from todo.services.scheduler import show_reminder_popup
        show_reminder_popup("这是一个测试提醒", "测试提醒")
        print("✅ 弹窗测试成功")
        return True
    except Exception as e:
        print(f"❌ 弹窗测试失败: {e}")
        return False

def test_scheduler():
    """测试调度器功能"""
    print("🧪 测试调度器功能...")
    try:
        from todo.services.scheduler import get_scheduler
        scheduler = get_scheduler()
        
        print(f"调度器运行状态: {scheduler.is_running()}")
        
        if not scheduler.is_running():
            print("启动调度器...")
            scheduler.start()
            time.sleep(1)
            print(f"调度器运行状态: {scheduler.is_running()}")
        
        print(f"当前作业数量: {scheduler.get_job_count()}")
        
        return True
    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        return False

def test_service_status():
    """测试服务状态检查"""
    print("🧪 测试服务状态检查...")
    try:
        from todo.services.scheduler import is_service_running
        status = is_service_running()
        print(f"服务运行状态: {status}")
        return True
    except Exception as e:
        print(f"❌ 服务状态检查失败: {e}")
        return False

def test_database():
    """测试数据库连接和提醒查询"""
    print("🧪 测试数据库功能...")
    try:
        from todo.database import get_session
        from todo.models.reminder import Reminder
        
        db = get_session()
        try:
            # 查询活跃提醒
            active_reminders = db.query(Reminder).filter(Reminder.is_active == True).all()
            print(f"活跃提醒数量: {len(active_reminders)}")
            
            for reminder in active_reminders:
                print(f"  - [{reminder.id}] {reminder.message} @ {reminder.notice_time}")
            
            return True
        finally:
            db.close()
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def create_test_reminder():
    """创建一个测试提醒（1分钟后触发）"""
    print("🧪 创建测试提醒...")
    try:
        from todo.database import get_session
        from todo.models.reminder import Reminder, RepeatMode
        from todo.services.scheduler import get_scheduler
        
        # 创建1分钟后的提醒
        notice_time = datetime.now() + timedelta(minutes=1)
        
        reminder = Reminder(
            message="这是一个测试提醒（1分钟后触发）",
            title="测试提醒",
            notice_time=notice_time,
            repeat_mode=RepeatMode.ONCE
        )
        reminder.job_id = reminder.generate_job_id()
        
        db = get_session()
        try:
            db.add(reminder)
            db.commit()
            db.refresh(reminder)
            
            print(f"✅ 创建提醒成功，ID: {reminder.id}")
            print(f"   触发时间: {notice_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 尝试添加到调度器
            scheduler = get_scheduler()
            if scheduler.add_reminder(reminder):
                print("✅ 提醒已添加到调度器")
            else:
                print("⚠️ 提醒添加到调度器失败")
            
            return True
        finally:
            db.close()
    except Exception as e:
        print(f"❌ 创建测试提醒失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 开始调试提醒功能...\n")
    
    tests = [
        ("弹窗功能", test_popup),
        ("数据库功能", test_database),
        ("调度器功能", test_scheduler),
        ("服务状态检查", test_service_status),
        ("创建测试提醒", create_test_reminder),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {name}")
        print('='*50)
        result = test_func()
        results.append((name, result))
        time.sleep(1)
    
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print('='*50)
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    print(f"\n🎯 建议:")
    print("1. 如果弹窗测试失败，检查tkinter是否正确安装")
    print("2. 如果调度器测试失败，检查APScheduler配置")
    print("3. 如果服务状态检查失败，确保daemon进程正在运行")
    print("4. 创建测试提醒后，等待1分钟看是否弹出提醒")

if __name__ == "__main__":
    main()
