#!/usr/bin/env python3
"""
Todo提醒服务守护进程运行器

用于在Windows系统上启动后台提醒服务的独立脚本。
"""

import sys
import time
import os
import signal
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('scheduler_daemon.log')
    ]
)
logger = logging.getLogger(__name__)

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import typer
    from todo.services.scheduler import get_scheduler
except ImportError as e:
    print(f"Failed to import required modules: {e}")
    sys.exit(1)


def write_pid_file(pid):
    """写入PID文件"""
    app_dir = typer.get_app_dir("todo-cli")
    os.makedirs(app_dir, exist_ok=True)
    pid_file = Path(app_dir) / "scheduler.pid"
    with open(pid_file, 'w') as f:
        f.write(str(pid))


def remove_pid_file():
    """删除PID文件"""
    app_dir = typer.get_app_dir("todo-cli")
    pid_file = Path(app_dir) / "scheduler.pid"
    if pid_file.exists():
        pid_file.unlink()


def setup_signal_handlers(scheduler):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"Received signal {signum}, stopping scheduler...")
        scheduler.stop()
        remove_pid_file()
        sys.exit(0)
    
    # 设置信号处理器
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    if hasattr(signal, 'SIGINT'):
        signal.signal(signal.SIGINT, signal_handler)


def main():
    """主函数"""
    try:
        # 写入当前进程PID
        current_pid = os.getpid()
        write_pid_file(current_pid)
        logger.info(f"Daemon started with PID: {current_pid}")

        # 获取调度器实例
        scheduler = get_scheduler()
        logger.info("Scheduler instance created")

        # 设置信号处理器
        setup_signal_handlers(scheduler)
        logger.info("Signal handlers set up")

        # 启动调度器
        scheduler.start()
        logger.info("Scheduler started successfully")

        # 强制加载现有的提醒
        try:
            scheduler._load_existing_reminders()
            logger.info("Existing reminders loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load existing reminders: {e}")

        logger.info("Daemon is now running, waiting for events...")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("Received keyboard interrupt, stopping...")
            scheduler.stop()
            remove_pid_file()
            
    except Exception as e:
        print(f"Error in daemon: {e}")
        remove_pid_file()
        sys.exit(1)


if __name__ == "__main__":
    main()
