"""
Database configuration and initialization module.

This module handles SQLAlchemy setup, database connection, and table creation.
"""

import os
from pathlib import Path
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
import typer

# 创建基础模型类
Base = declarative_base()

# 数据库配置
def get_db_path() -> Path:
    """获取数据库文件路径"""
    app_dir = typer.get_app_dir("todo-cli")
    os.makedirs(app_dir, exist_ok=True)
    return Path(app_dir) / "todo.db"

# 创建数据库引擎
DATABASE_URL = f"sqlite:///{get_db_path()}"
engine = create_engine(
    DATABASE_URL,
    echo=False,  # 设置为True可以看到SQL语句
    connect_args={"check_same_thread": False}  # SQLite特定配置
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """初始化数据库，创建所有表"""
    # 导入所有模型以确保它们被注册到Base.metadata
    from todo.models import task, category, tag, reminder

    # 创建所有表
    Base.metadata.create_all(bind=engine)

    # 检查并升级数据库结构
    check_and_upgrade_db()


def get_session():
    """获取数据库会话（用于命令行操作）"""
    return SessionLocal()


def check_and_upgrade_db():
    """检查并升级数据库结构"""
    from sqlalchemy import inspect, text

    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()

    # 检查是否需要添加notice_time字段到tasks表
    if 'tasks' in existing_tables:
        columns = [col['name'] for col in inspector.get_columns('tasks')]
        if 'notice_time' not in columns:
            print("Adding notice_time column to tasks table...")
            with engine.connect() as conn:
                conn.execute(text('ALTER TABLE tasks ADD COLUMN notice_time DATETIME'))
                conn.commit()

    # 检查是否需要创建reminders表
    if 'reminders' not in existing_tables:
        print("Creating reminders table...")
        # 导入reminder模型以确保表结构被注册
        from todo.models import reminder
        Base.metadata.create_all(bind=engine, tables=[reminder.Reminder.__table__])

    # 检查是否需要创建scheduler_jobs表（APScheduler使用）
    if 'scheduler_jobs' not in existing_tables:
        print("Creating scheduler_jobs table...")
        # APScheduler会自动创建这个表，这里只是确保它存在
        pass


def upgrade_database():
    """升级数据库结构，添加提醒功能相关表"""
    try:
        print("Checking database structure...")
        check_and_upgrade_db()
        print("Database upgrade completed successfully!")
        return True
    except Exception as e:
        print(f"Database upgrade failed: {e}")
        return False
