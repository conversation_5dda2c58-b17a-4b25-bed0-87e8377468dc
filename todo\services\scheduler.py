"""
APScheduler-based notification scheduler service.

This module provides the NoticeScheduler class for managing scheduled reminders
using APScheduler with SQLAlchemy job store for persistence.
"""

import logging
import os
import signal
import sys
import tkinter as tk
from tkinter import messagebox
from datetime import datetime, timedelta
from typing import Optional, List
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from sqlalchemy.orm import Session

from todo.database import get_session, DATABASE_URL
from todo.models.reminder import Reminder, RepeatMode
from todo.models.task import Task

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def show_reminder_popup(message: str, title: str = "任务提醒", reminder_id: Optional[int] = None):
    """显示提醒弹窗"""
    try:
        logger.info(f"Attempting to show reminder popup: {title} - {message}")

        # 创建主窗口并隐藏
        root = tk.Tk()
        root.withdraw()

        # Windows特殊处理
        if os.name == 'nt':
            root.wm_attributes('-topmost', True)
            root.lift()
            root.focus_force()
        else:
            root.attributes('-topmost', True)

        # 显示提醒弹窗
        messagebox.showinfo(title, message)
        root.destroy()

        logger.info(f"Reminder popup shown successfully: {title} - {message}")

        # 如果是一次性提醒，标记为已完成
        if reminder_id:
            mark_reminder_completed(reminder_id)

    except Exception as e:
        logger.error(f"Failed to show reminder popup: {e}")
        import traceback
        logger.error(traceback.format_exc())


def mark_reminder_completed(reminder_id: int):
    """标记一次性提醒为已完成"""
    db = get_session()
    try:
        reminder = db.query(Reminder).filter(Reminder.id == reminder_id).first()
        if reminder and reminder.repeat_mode == RepeatMode.ONCE:
            reminder.is_active = False
            db.commit()
            logger.info(f"Reminder {reminder_id} marked as completed")
    except Exception as e:
        logger.error(f"Failed to mark reminder as completed: {e}")
        db.rollback()
    finally:
        db.close()


class NoticeScheduler:
    """APScheduler-based notification scheduler"""
    
    def __init__(self):
        """初始化调度器"""
        # 配置作业存储
        jobstores = {
            'default': SQLAlchemyJobStore(url=DATABASE_URL, tablename='scheduler_jobs')
        }
        
        # 配置执行器
        executors = {
            'default': ThreadPoolExecutor(20)
        }
        
        # 作业默认设置
        job_defaults = {
            'coalesce': False,  # 不合并作业
            'max_instances': 3  # 最大实例数
        }
        
        # 创建调度器
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """设置信号处理器，确保优雅关闭"""
        def signal_handler(signum, frame):
            logger.info("Received shutdown signal, stopping scheduler...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start(self):
        """启动调度器"""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("Scheduler started successfully")
                self._load_existing_reminders()
                # 启动定期检查新提醒的任务
                self._start_reminder_monitor()
            else:
                logger.warning("Scheduler is already running")
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            raise
    
    def stop(self):
        """停止调度器"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown(wait=True)
                logger.info("Scheduler stopped successfully")
            else:
                logger.warning("Scheduler is not running")
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {e}")
    
    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        try:
            return self.scheduler.running
        except Exception:
            return False
    
    def _create_trigger(self, reminder: Reminder):
        """根据提醒模式创建触发器"""
        if reminder.repeat_mode == RepeatMode.ONCE:
            logger.info(f"Creating DateTrigger for {reminder.notice_time}")
            return DateTrigger(run_date=reminder.notice_time)
        
        elif reminder.repeat_mode == RepeatMode.EVERY_DAY:
            return CronTrigger(
                hour=reminder.notice_time.hour,
                minute=reminder.notice_time.minute
            )
        
        elif reminder.repeat_mode == RepeatMode.WORKING_DAY:
            return CronTrigger(
                day_of_week='mon-fri',
                hour=reminder.notice_time.hour,
                minute=reminder.notice_time.minute
            )
        
        elif reminder.repeat_mode == RepeatMode.WEEK:
            return CronTrigger(
                day_of_week=reminder.notice_time.weekday(),
                hour=reminder.notice_time.hour,
                minute=reminder.notice_time.minute
            )
        
        elif reminder.repeat_mode == RepeatMode.MONTH:
            return CronTrigger(
                day=reminder.notice_time.day,
                hour=reminder.notice_time.hour,
                minute=reminder.notice_time.minute
            )
        
        elif reminder.repeat_mode == RepeatMode.YEAR:
            return CronTrigger(
                month=reminder.notice_time.month,
                day=reminder.notice_time.day,
                hour=reminder.notice_time.hour,
                minute=reminder.notice_time.minute
            )
        
        else:
            raise ValueError(f"Unsupported repeat mode: {reminder.repeat_mode}")
    
    def add_reminder(self, reminder: Reminder) -> bool:
        """添加提醒任务到调度器"""
        try:
            if not reminder.job_id:
                reminder.job_id = reminder.generate_job_id()

            trigger = self._create_trigger(reminder)

            # 确定提醒标题
            title = "任务提醒" if reminder.is_task_reminder else "提醒通知"
            if reminder.title:
                title = reminder.title

            # 如果调度器未运行，只记录日志，不添加作业
            if not self.scheduler.running:
                logger.warning(f"Scheduler not running, reminder will be loaded on next start: {reminder.job_id}")
                return True

            # 添加作业
            job = self.scheduler.add_job(
                func=show_reminder_popup,
                trigger=trigger,
                args=[reminder.message, title, reminder.id if reminder.repeat_mode == RepeatMode.ONCE else None],
                id=reminder.job_id,
                replace_existing=True
            )

            logger.info(f"Added reminder job: {reminder.job_id}, next run: {job.next_run_time}")
            return True

        except Exception as e:
            logger.error(f"Failed to add reminder: {e}")
            return False
    
    def remove_reminder(self, job_id: str) -> bool:
        """从调度器中移除提醒任务"""
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Removed reminder job: {job_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to remove reminder: {e}")
            return False
    
    def _start_reminder_monitor(self):
        """启动提醒监控任务，定期检查新的提醒"""
        try:
            # 每30秒检查一次新的提醒
            self.scheduler.add_job(
                func=self._check_new_reminders,
                trigger='interval',
                seconds=30,
                id='reminder_monitor',
                replace_existing=True
            )
            logger.info("Reminder monitor started (checking every 30 seconds)")
        except Exception as e:
            logger.error(f"Failed to start reminder monitor: {e}")

    def _check_new_reminders(self):
        """检查并加载新的提醒"""
        try:
            db = get_session()
            try:
                # 获取所有活跃且未被调度的提醒
                active_reminders = db.query(Reminder).filter(
                    Reminder.is_active == True
                ).all()

                # 获取当前调度器中的作业ID
                current_job_ids = {job.id for job in self.scheduler.get_jobs()}

                new_count = 0
                for reminder in active_reminders:
                    # 跳过已经在调度器中的提醒
                    if reminder.job_id in current_job_ids:
                        continue

                    # 对于一次性提醒，检查是否已过期
                    if reminder.repeat_mode == RepeatMode.ONCE and reminder.notice_time <= datetime.now():
                        # 标记为非活跃
                        reminder.is_active = False
                        logger.info(f"Deactivated expired reminder: {reminder.id}")
                        continue

                    # 添加新的提醒到调度器
                    if self.add_reminder(reminder):
                        new_count += 1
                        logger.info(f"Added new reminder to scheduler: {reminder.id}")

                if new_count > 0:
                    logger.info(f"Added {new_count} new reminders to scheduler")

                # 提交数据库更改
                db.commit()

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Failed to check new reminders: {e}")

    def _load_existing_reminders(self):
        """加载现有的活跃提醒到调度器"""
        db = get_session()
        try:
            # 获取所有活跃的提醒
            active_reminders = db.query(Reminder).filter(Reminder.is_active == True).all()

            loaded_count = 0
            for reminder in active_reminders:
                # 对于一次性提醒，检查是否已过期
                if reminder.repeat_mode == RepeatMode.ONCE and reminder.notice_time <= datetime.now():
                    # 标记为非活跃
                    reminder.is_active = False
                    logger.info(f"Deactivated expired reminder: {reminder.id}")
                    continue

                # 对于重复提醒或未过期的一次性提醒，添加到调度器
                if self.add_reminder(reminder):
                    loaded_count += 1

            # 提交数据库更改
            db.commit()
            logger.info(f"Loaded {loaded_count} existing reminders")

        except Exception as e:
            logger.error(f"Failed to load existing reminders: {e}")
            db.rollback()
        finally:
            db.close()
    
    def get_job_count(self) -> int:
        """获取当前作业数量"""
        return len(self.scheduler.get_jobs())
    
    def list_jobs(self) -> List[dict]:
        """列出所有作业信息"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time,
                'trigger': str(job.trigger)
            })
        return jobs


# 全局调度器实例
_scheduler_instance: Optional[NoticeScheduler] = None


def is_service_running() -> bool:
    """检查提醒服务是否在运行"""
    import typer
    import psutil
    from pathlib import Path

    try:
        app_dir = typer.get_app_dir("todo-cli")
        pid_file = Path(app_dir) / "scheduler.pid"

        if not pid_file.exists():
            return False

        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        # 检查进程是否存在
        if psutil.pid_exists(pid):
            proc = psutil.Process(pid)
            # 检查进程名称是否匹配
            if 'python' in proc.name().lower():
                return True

        return False

    except Exception:
        return False


def get_scheduler() -> NoticeScheduler:
    """获取全局调度器实例"""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = NoticeScheduler()
    return _scheduler_instance
