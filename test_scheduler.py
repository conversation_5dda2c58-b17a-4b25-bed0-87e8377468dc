#!/usr/bin/env python3
"""
测试调度器配置的简单脚本
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from todo.services.scheduler import get_scheduler
    print("✅ 成功导入调度器模块")
    
    # 测试创建调度器实例
    scheduler = get_scheduler()
    print("✅ 成功创建调度器实例")
    
    # 测试启动调度器
    scheduler.start()
    print("✅ 成功启动调度器")
    
    # 检查调度器状态
    print(f"调度器运行状态: {scheduler.is_running()}")
    print(f"当前作业数量: {scheduler.get_job_count()}")
    
    # 停止调度器
    scheduler.stop()
    print("✅ 成功停止调度器")
    
    print("\n🎉 调度器配置测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
