#!/usr/bin/env python3
"""
简单的提醒功能测试脚本
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_immediate_popup():
    """测试立即弹窗"""
    print("🧪 测试立即弹窗...")
    try:
        from todo.services.scheduler import show_reminder_popup
        show_reminder_popup("这是一个立即测试提醒", "立即测试")
        print("✅ 立即弹窗测试成功")
        return True
    except Exception as e:
        print(f"❌ 立即弹窗测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scheduler_with_immediate_job():
    """测试调度器立即执行任务"""
    print("🧪 测试调度器立即执行任务...")
    try:
        from todo.services.scheduler import get_scheduler, show_reminder_popup
        from apscheduler.triggers.date import DateTrigger
        
        scheduler = get_scheduler()
        
        # 启动调度器
        if not scheduler.is_running():
            scheduler.start()
            print("调度器已启动")
        
        # 添加一个5秒后执行的任务
        run_time = datetime.now() + timedelta(seconds=5)
        print(f"添加任务，将在 {run_time.strftime('%H:%M:%S')} 执行")
        
        job = scheduler.scheduler.add_job(
            func=show_reminder_popup,
            trigger=DateTrigger(run_date=run_time),
            args=["这是5秒后的测试提醒", "调度器测试"],
            id="test_job_5sec"
        )
        
        print(f"任务已添加，下次运行时间: {job.next_run_time}")
        print("等待5秒...")
        
        # 等待任务执行
        time.sleep(7)
        
        print("✅ 调度器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_reminder():
    """测试通过数据库创建提醒"""
    print("🧪 测试通过数据库创建提醒...")
    try:
        from todo.database import get_session
        from todo.models.reminder import Reminder, RepeatMode
        from todo.services.scheduler import get_scheduler
        
        # 创建10秒后的提醒
        notice_time = datetime.now() + timedelta(seconds=10)
        
        reminder = Reminder(
            message="这是通过数据库创建的10秒后提醒",
            title="数据库测试",
            notice_time=notice_time,
            repeat_mode=RepeatMode.ONCE
        )
        reminder.job_id = reminder.generate_job_id()
        
        # 保存到数据库
        db = get_session()
        try:
            db.add(reminder)
            db.commit()
            db.refresh(reminder)
            print(f"提醒已保存到数据库，ID: {reminder.id}")
        finally:
            db.close()
        
        # 添加到调度器
        scheduler = get_scheduler()
        if not scheduler.is_running():
            scheduler.start()
        
        success = scheduler.add_reminder(reminder)
        if success:
            print(f"提醒已添加到调度器，将在 {notice_time.strftime('%H:%M:%S')} 执行")
            print("等待10秒...")
            time.sleep(12)
            print("✅ 数据库提醒测试完成")
            return True
        else:
            print("❌ 添加到调度器失败")
            return False
        
    except Exception as e:
        print(f"❌ 数据库提醒测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 开始简单提醒功能测试...\n")
    
    tests = [
        ("立即弹窗测试", test_immediate_popup),
        ("调度器5秒测试", test_scheduler_with_immediate_job),
        ("数据库10秒测试", test_database_reminder),
    ]
    
    for name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {name}")
        print('='*50)
        
        result = test_func()
        
        if result:
            print(f"✅ {name} 通过")
        else:
            print(f"❌ {name} 失败")
            break  # 如果某个测试失败，停止后续测试
        
        print("\n等待2秒后继续...")
        time.sleep(2)
    
    print(f"\n{'='*50}")
    print("测试完成")
    print('='*50)

if __name__ == "__main__":
    main()
