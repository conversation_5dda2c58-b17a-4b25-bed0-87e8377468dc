2025-08-05 17:56:26,446 - __main__ - INFO - Daemon started with PID: 9944
2025-08-05 17:56:26,456 - __main__ - INFO - Scheduler instance created
2025-08-05 17:56:26,456 - __main__ - INFO - Signal handlers set up
2025-08-05 17:56:26,457 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 17:56:26,460 - todo.services.scheduler - INFO - Scheduler started successfully
2025-08-05 17:56:26,468 - todo.services.scheduler - INFO - Deactivated expired reminder: 7
2025-08-05 17:56:26,468 - todo.services.scheduler - INFO - Deactivated expired reminder: 8
2025-08-05 17:56:26,473 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 17:56:26,473 - __main__ - INFO - Scheduler started successfully
2025-08-05 17:56:26,474 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 17:56:26,474 - __main__ - INFO - Existing reminders loaded successfully
2025-08-05 17:56:26,474 - __main__ - INFO - <PERSON> is now running, waiting for events...
2025-08-05 18:01:27,379 - __main__ - INFO - Daemon started with PID: 19940
2025-08-05 18:01:27,385 - __main__ - INFO - Scheduler instance created
2025-08-05 18:01:27,385 - __main__ - INFO - Signal handlers set up
2025-08-05 18:01:27,387 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:01:27,388 - todo.services.scheduler - INFO - Scheduler started successfully
2025-08-05 18:01:27,399 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 18:01:27,400 - todo.services.scheduler - ERROR - Failed to start reminder monitor: Schedulers cannot be serialized. Ensure that you are not passing a scheduler instance as an argument to a job, or scheduling an instance method where the instance contains a scheduler as an attribute.
2025-08-05 18:01:27,400 - __main__ - INFO - Scheduler started successfully
2025-08-05 18:01:27,400 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 18:01:27,400 - __main__ - INFO - Existing reminders loaded successfully
2025-08-05 18:01:27,401 - __main__ - INFO - Daemon is now running, waiting for events...
2025-08-05 18:08:58,827 - __main__ - INFO - Daemon started with PID: 20524
2025-08-05 18:08:58,833 - __main__ - INFO - Scheduler instance created
2025-08-05 18:08:58,833 - __main__ - INFO - Signal handlers set up
2025-08-05 18:08:58,834 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:08:58,836 - todo.services.scheduler - INFO - Scheduler started successfully
2025-08-05 18:08:58,845 - todo.services.scheduler - INFO - Deactivated expired reminder: 11
2025-08-05 18:08:58,849 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 18:08:58,850 - todo.services.scheduler - ERROR - Failed to start reminder monitor: Schedulers cannot be serialized. Ensure that you are not passing a scheduler instance as an argument to a job, or scheduling an instance method where the instance contains a scheduler as an attribute.
2025-08-05 18:08:58,850 - __main__ - INFO - Scheduler started successfully
2025-08-05 18:08:58,850 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 18:08:58,850 - __main__ - INFO - Existing reminders loaded successfully
2025-08-05 18:08:58,850 - __main__ - INFO - Daemon is now running, waiting for events...
2025-08-05 18:09:24,681 - __main__ - INFO - Daemon started with PID: 11032
2025-08-05 18:09:24,688 - __main__ - INFO - Scheduler instance created
2025-08-05 18:09:24,688 - __main__ - INFO - Signal handlers set up
2025-08-05 18:09:24,690 - apscheduler.scheduler - INFO - Scheduler started
2025-08-05 18:09:24,692 - todo.services.scheduler - INFO - Scheduler started successfully
2025-08-05 18:09:24,701 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 18:09:24,702 - todo.services.scheduler - ERROR - Failed to start reminder monitor: Schedulers cannot be serialized. Ensure that you are not passing a scheduler instance as an argument to a job, or scheduling an instance method where the instance contains a scheduler as an attribute.
2025-08-05 18:09:24,702 - __main__ - INFO - Scheduler started successfully
2025-08-05 18:09:24,702 - todo.services.scheduler - INFO - Loaded 0 existing reminders
2025-08-05 18:09:24,702 - __main__ - INFO - Existing reminders loaded successfully
2025-08-05 18:09:24,702 - __main__ - INFO - Daemon is now running, waiting for events...
