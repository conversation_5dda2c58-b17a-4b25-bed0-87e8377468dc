[project]
name = "todo-cli"
version = "0.1.0"
description = "A powerful CLI todo task management tool built with Typer and SQLAlchemy"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "typer>=0.16.0",
    "sqlalchemy>=2.0.0",
    "rich>=13.0.0",
    "click>=8.0.0",
    "langchain[openai]>=0.3.27",
    "apscheduler>=3.11.0",
]

[project.scripts]
todo = "todo.main:app"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["todo"]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
