"""
Reminder model for notification management.

This module defines the Remind<PERSON> model for managing scheduled notifications
and the RepeatMode enum for different repeat patterns.
"""

from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Enum as SQLEnum
from sqlalchemy.sql import func
from todo.database import Base


class RepeatMode(Enum):
    """重复模式枚举"""
    ONCE = "once"
    EVERY_DAY = "every_day"
    WORKING_DAY = "working_day"
    WEEK = "week"
    MONTH = "month"
    YEAR = "year"


class Reminder(Base):
    """Reminder model for scheduled notifications."""
    
    __tablename__ = "reminders"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True)
    
    # 基本信息
    message = Column(String(500), nullable=False)
    title = Column(String(200), nullable=True)  # 可选标题
    
    # 时间信息
    notice_time = Column(DateTime(timezone=True), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 重复设置
    repeat_mode = Column(SQLEnum(RepeatMode), default=RepeatMode.ONCE, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    
    # 调度器相关
    job_id = Column(String(100), unique=True, nullable=True)  # APScheduler job ID
    
    # 关联任务（可选）
    task_id = Column(Integer, nullable=True, index=True)  # 关联的任务ID，用于任务提醒
    
    def __repr__(self):
        return f"<Reminder(id={self.id}, message='{self.message[:50]}...', repeat='{self.repeat_mode.value}')>"
    
    def __str__(self):
        return f"[{self.id}] {self.message[:50]}{'...' if len(self.message) > 50 else ''}"
    
    @property
    def is_task_reminder(self):
        """检查是否为任务提醒"""
        return self.task_id is not None
    
    @property
    def is_repeating(self):
        """检查是否为重复提醒"""
        return self.repeat_mode != RepeatMode.ONCE
    
    def generate_job_id(self):
        """生成唯一的job ID"""
        prefix = "task_reminder" if self.is_task_reminder else "notice"
        return f"{prefix}_{self.id}_{self.notice_time.strftime('%Y%m%d_%H%M%S')}"
