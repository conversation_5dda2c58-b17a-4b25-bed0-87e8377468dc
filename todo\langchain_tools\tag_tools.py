from typing import Optional, Dict, Any
from langchain_core.tools import tool

from todo.database import get_session
from todo.models.tag import Tag

@tool
def add_tag(
    name: str,
    description: Optional[str] = None,
    color: Optional[str] = None
) -> Dict[str, Any]:
    """创建新标签

    Args:
        name: 标签名称
        description: 标签描述（可选）
        color: 标签颜色，十六进制格式如 #FF5733（可选）

    Returns:
        包含标签信息的字典
    """
    db = get_session()
    try:
        # 检查标签是否已存在
        existing = db.query(Tag).filter(Tag.name == name).first()
        if existing:
            return {"error": f"Tag '{name}' already exists"}

        # 验证颜色格式
        if color and not color.startswith('#'):
            color = f"#{color}"

        if color and len(color) != 7:
            return {"error": "Color must be a valid hex code (e.g., #FF5733)"}

        # 创建标签
        tag = Tag(
            name=name,
            description=description,
            color=color
        )

        db.add(tag)
        db.commit()
        db.refresh(tag)

        return {
            "success": True,
            "tag_id": tag.id,
            "name": tag.name,
            "description": tag.description,
            "color": tag.color,
            "created_at": tag.created_at.isoformat() if tag.created_at else None
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to create tag: {str(e)}"}
    finally:
        db.close()


@tool
def list_tags() -> Dict[str, Any]:
    """列出所有标签

    Returns:
        包含标签列表的字典
    """
    db = get_session()
    try:
        tags = db.query(Tag).order_by(Tag.name).all()

        tag_list = []
        for tag in tags:
            tag_list.append({
                "id": tag.id,
                "name": tag.name,
                "description": tag.description,
                "color": tag.color,
                "task_count": tag.task_count,
                "created_at": tag.created_at.isoformat() if tag.created_at else None
            })

        return {
            "success": True,
            "tags": tag_list,
            "total_count": len(tag_list)
        }

    except Exception as e:
        return {"error": f"Failed to list tags: {str(e)}"}
    finally:
        db.close()


@tool
def update_tag(
    name: str,
    new_name: Optional[str] = None,
    description: Optional[str] = None,
    color: Optional[str] = None
) -> Dict[str, Any]:
    """更新标签

    Args:
        name: 要更新的标签名称
        new_name: 新的标签名称
        description: 新的描述
        color: 新的颜色（十六进制格式）

    Returns:
        包含更新结果的字典
    """
    db = get_session()
    try:
        tag = db.query(Tag).filter(Tag.name == name).first()
        if not tag:
            return {"error": f"Tag '{name}' not found"}

        updated = False

        if new_name:
            # 检查新名称是否已存在
            existing = db.query(Tag).filter(Tag.name == new_name).first()
            if existing and existing.id != tag.id:
                return {"error": f"Tag '{new_name}' already exists"}
            tag.name = new_name
            updated = True

        if description is not None:
            tag.description = description
            updated = True

        if color:
            if not color.startswith('#'):
                color = f"#{color}"
            if len(color) != 7:
                return {"error": "Color must be a valid hex code (e.g., #FF5733)"}
            tag.color = color
            updated = True

        if not updated:
            return {"error": "No changes specified"}

        db.commit()

        return {
            "success": True,
            "message": f"Tag updated successfully",
            "tag": {
                "id": tag.id,
                "name": tag.name,
                "description": tag.description,
                "color": tag.color,
                "updated_at": tag.updated_at.isoformat() if tag.updated_at else None
            }
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to update tag: {str(e)}"}
    finally:
        db.close()


@tool
def delete_tag(name: str) -> Dict[str, Any]:
    """删除标签

    Args:
        name: 标签名称

    Returns:
        包含删除结果的字典
    """
    db = get_session()
    try:
        tag = db.query(Tag).filter(Tag.name == name).first()
        if not tag:
            return {"error": f"Tag '{name}' not found"}

        # 检查是否有任务使用该标签
        if tag.task_count > 0:
            return {"error": f"Cannot delete tag '{name}' because it has {tag.task_count} associated tasks"}

        db.delete(tag)
        db.commit()

        return {
            "success": True,
            "message": f"Tag '{name}' deleted successfully"
        }

    except Exception as e:
        db.rollback()
        return {"error": f"Failed to delete tag: {str(e)}"}
    finally:
        db.close()

TAG_TOOLS = [add_tag, list_tags, update_tag, delete_tag]