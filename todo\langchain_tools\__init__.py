"""
Langchain 工具方法模块

将 Todo CLI 的功能封装为 Langchain 工具，支持 AI 集成调用。
包含任务、分类、标签、提醒的完整 CRUD 操作。
"""

# 导入各模块的工具
from .common_tools import COMMON_TOOLS
from .task_tools import TASK_TOOLS
from .category_tools import CATEGORY_TOOLS
from .reminder_tools import REMINDER_TOOLS
from .tag_tools import TAG_TOOLS

# ==================== 工具列表导出 ====================

# 所有可用的工具列表
ALL_TOOLS = COMMON_TOOLS + TASK_TOOLS + REMINDER_TOOLS + CATEGORY_TOOLS + TAG_TOOLS

# 按功能分组的工具导出
__all__ = [
    "ALL_TOOLS",
    "COMMON_TOOLS",
    "TASK_TOOLS",
    "REMINDER_TOOLS",
    "CATEGORY_TOOLS",
    "TAG_TOOLS",
]
